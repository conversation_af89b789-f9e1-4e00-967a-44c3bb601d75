package com.example.myapplication.features.works.domain.repository

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.api.WorkApiService
import com.example.myapplication.features.works.data.remote.dto.CreateWorkDto
import com.example.myapplication.features.works.data.remote.dto.GetWorksDto
import com.example.myapplication.features.works.data.remote.dto.GetWorksParametersDto
import com.example.myapplication.features.works.data.remote.dto.WorkDto

class WorksRepositopyImpl(
    private val worksService: WorkApiService
): WorksRepository {

    override suspend fun createWork(createWorkRequest: CreateWorkDto): ResultAsyncState<WorkDto> {
        try {
            val response = worksService.createWork(createWorkRequest)
            if (!response.isSuccessful) {
                throw Exception("Erro ao criar obra: ${response.message()}")
            }
        } catch (e: Exception) {
            throw Exception("Erro ao criar obra: ${e.message}")
        }
        return ResultAsyncState.Error( "Erro ao criar obra")
    }

    private fun GetWorksParametersDto?.toQueryMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        this?.let {
            type?.let { map["type"] = it.value }
            status?.let { map["status"] = it.value }
            author?.let { map["author"] = it }
            artist?.let { map["artist"] = it }
            search?.let { map["search"] = it }
            tags?.let { map["tags"] = it.joinToString(",") }
            minRating?.let { map["minRating"] = it }
            maxRating?.let { map["maxRating"] = it }
            page?.let { map["page"] = it }
            limit?.let { map["limit"] = it }
            sortBy?.let { map["sortBy"] = it.value }
            sortOrder?.let { map["sortOrder"] = it.value }
        }
        return map
    }

    override suspend fun getWorks(params: GetWorksParametersDto?): ResultAsyncState<GetWorksDto> {
        return try {
            val queryMap = params?.toQueryMap() ?: emptyMap()
            val response = worksService.getWorks(queryMap)
            if (response.isSuccessful) {
                val works = response.body()?.data
                ResultAsyncState.Success(
                    works ?: GetWorksDto(
                        works = emptyList(),
                        page = 1,
                        limit = 10,
                        totalPages = 1,
                        total = 1
                    )
                )
            } else {
                ResultAsyncState.Error("Erro ao buscar obras: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao buscar obras: ${e.message}")
        }
    }
}